'use client'

import { useRouter } from 'next/navigation'
import { useEffect, useState } from 'react'
import { trim, isEmpty } from 'lodash'
import { useLazyQuery, useMutation } from '@apollo/client'
import Loading from '@/app/loading'
import {
    READ_ONE_SEALOGS_GROUP,
    READ_PERMISSION_TYPES,
} from '@/app/lib/graphQL/query'
import {
    UPDATE_SEALOGS_GROUP,
    CREATE_SEALOGS_GROUP,
} from '@/app/lib/graphQL/mutation'

import {
    isAdmin as isAdminRole,
    preventCrewAccess,
} from '@/app/helpers/userHelper'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { FooterWrapper } from '@/components/footer-wrapper'
import SeaLogsButton from '@/components/ui/sea-logs-button'
import {
    Alert,
    Button,
    Card,
    CardContent,
    CardTitle,
    Checkbox,
    H3,
    Textarea,
} from '@/components/ui'

const UserRoleForm = ({ roleID = 0 }: { roleID: number }) => {
    const [isUserAdmin, setIsUserAdmin] = useState<any>(-1)
    const router = useRouter()
    const [userRole, setUserRole] = useState<any>({})
    const [formError, setFormError] = useState<any>({})
    const [permissions, setPermissions] = useState<any>([])
    const [isLoading, setIsLoading] = useState(true)
    const [userPermissions, setUserPermissions] = useState<any>([])
    const [isAdmin, setIsAdmin] = useState(false)
    useEffect(() => {
        setIsUserAdmin(isAdminRole())
    }, [])
    const handleInputOnChange = (event: any) => {
        const { name, value } = event.target
        setUserRole({ ...userRole, id: roleID, [name]: value })
    }
    const [createSeaLogsGroup, { loading: createSeaLogsGroupLoading }] =
        useMutation(CREATE_SEALOGS_GROUP, {
            onCompleted: (response: any) => {
                const data = response.createSeaLogsGroup
                if (data) {
                    router.push('/settings/user-role')
                }
            },
            onError: (error: any) => {
                // console.error('createSeaLogsGroup error', error.message)
                setFormError({ ...formError, response: error.message })
            },
        })
    const [updateSeaLogsGroup, { loading: updateSeaLogsGroupLoading }] =
        useMutation(UPDATE_SEALOGS_GROUP, {
            onCompleted: (response: any) => {
                const data = response.updateSeaLogsGroup
                if (data) {
                    router.push('/settings/user-role')
                }
            },
            onError: (error: any) => {
                setFormError({ ...formError, response: error.message })
            },
        })
    const handleSave = async () => {
        // Do not allow the group with code admin to be saved
        if (isAdmin) {
            return
        }
        if (isEmpty(trim(userRole?.title))) {
            setFormError({ ...formError, title: 'Title is required' })
            return
        }
        setFormError({})
        const input = { ...userRole }
        delete input.__typename
        delete input.permissions
        delete input.members
        input.permissionCodes = userPermissions.join(',')
        if (roleID > 0) {
            await updateSeaLogsGroup({
                variables: {
                    input: input,
                },
            })
        } else {
            await createSeaLogsGroup({
                variables: {
                    input: input,
                },
            })
        }
    }
    const [readOneSealLogsGroup, { loading: readOneSealLogsGroupLoading }] =
        useLazyQuery(READ_ONE_SEALOGS_GROUP, {
            fetchPolicy: 'cache-and-network',
            onCompleted: (response: any) => {
                const data = response.readOneSeaLogsGroup
                if (data) {
                    const codesArray = data.permissions.nodes.map(
                        (node: any) => node.code,
                    )
                    setUserPermissions(codesArray)
                    setUserRole(data)
                    const admin = data.code === 'admin'
                    setIsAdmin(admin)
                }
            },
            onError: (error: any) => {
                console.error('readOneSealLogsGroup error', error)
            },
        })
    const loadUserRole = async () => {
        await readOneSealLogsGroup({
            variables: {
                id: roleID,
            },
        })
    }
    const [readPermissionTypes, { loading: readPermissionTypesLoading }] =
        useLazyQuery(READ_PERMISSION_TYPES, {
            fetchPolicy: 'cache-and-network',
            onCompleted: (response: any) => {
                const data = response.readPermissionTypes
                if (data) {
                    // Group by categories
                    const groupedByCategory = data.reduce(
                        (acc: any, obj: any) => {
                            const { category, ...rest } = obj
                            acc[category] = acc[category] || []
                            acc[category].push(rest)
                            acc[category].sort(
                                (a: any, b: any) => a.sort > b.sort,
                            ) // Sort by name
                            return acc
                        },
                        {},
                    )

                    console.info('permissions', groupedByCategory)
                    setPermissions(groupedByCategory)
                }
            },
            onError: (error: any) => {
                console.error('readPermissionTypes error', error)
            },
        })
    const loadPermissions = async () => {
        await readPermissionTypes()
    }
    const handlePermissionCheckboxChange = (checked: boolean, code: string) => {
        setUserPermissions((prevPermissions: any) => {
            if (checked) {
                return [...prevPermissions, code]
            } else {
                return prevPermissions.filter(
                    (permission: any) => permission !== code,
                )
            }
        })
    }

    const handleModuleCheckboxChange = (checked: boolean, category: string) => {
        const categoryPermissions = permissions[category].map(
            (permission: any) => permission.code,
        )

        setUserPermissions((prevPermissions: any) => {
            if (checked) {
                // Add all permissions from this category that aren't already selected
                const newPermissions = [...prevPermissions]
                categoryPermissions.forEach((code: string) => {
                    if (!newPermissions.includes(code)) {
                        newPermissions.push(code)
                    }
                })
                return newPermissions
            } else {
                // Remove all permissions from this category
                return prevPermissions.filter(
                    (permission: any) =>
                        !categoryPermissions.includes(permission),
                )
            }
        })
    }

    const isModuleFullySelected = (category: string) => {
        const categoryPermissions =
            permissions[category]?.map((permission: any) => permission.code) ||
            []
        return (
            categoryPermissions.length > 0 &&
            categoryPermissions.every((code: string) =>
                userPermissions.includes(code),
            )
        )
    }

    const isModulePartiallySelected = (category: string) => {
        const categoryPermissions =
            permissions[category]?.map((permission: any) => permission.code) ||
            []
        return (
            categoryPermissions.some((code: string) =>
                userPermissions.includes(code),
            ) && !isModuleFullySelected(category)
        )
    }
    useEffect(() => {
        if (roleID > 0) {
            loadUserRole()
        }
    }, [roleID])
    useEffect(() => {
        if (isLoading) {
            preventCrewAccess()
            loadPermissions()
            setIsLoading(false)
        }
    }, [isLoading])
    useEffect(() => {
        if (roleID == 0) {
            let defaultPerms = Object.values(permissions).flatMap(
                (subItems: any) =>
                    subItems
                        .filter((item: any) => item.default === true)
                        .map((item: any) => item.code),
            )

            setUserPermissions(defaultPerms)
        }
    }, [permissions])

    if (isUserAdmin === false) {
        return (
            <Loading errorMessage="Oops You do not have the permission to view this section." />
        )
    }

    return (
        <>
            <Card className="mb-4">
                <CardTitle>
                    <H3>{roleID > 0 ? 'Edit' : 'New'} User Role</H3>
                </CardTitle>
                <CardContent className="flex flex-col gap-6">
                    <div className="grid grid-cols-1 md:grid-cols-3">
                        <H3 className="text-lg">Role Details</H3>
                        <div className="flex flex-col gap-4 md:col-span-2">
                            {formError?.response && (
                                <Alert variant={'destructive'}>
                                    {formError?.response}
                                </Alert>
                            )}
                            <div className="grid md:grid-cols-2 gap-4">
                                <Label label="Title">
                                    <Input
                                        name="title"
                                        type="text"
                                        value={userRole?.title ?? ''}
                                        placeholder="Title"
                                        onChange={handleInputOnChange}
                                        disabled={isAdmin}
                                    />
                                    <small className="text-red-500">
                                        {formError?.title}
                                    </small>
                                </Label>

                                <Label label="Code">
                                    <Input
                                        name="code"
                                        type="text"
                                        value={userRole?.code ?? ''}
                                        placeholder="Code"
                                        onChange={handleInputOnChange}
                                        disabled={isAdmin}
                                    />
                                </Label>
                            </div>
                            <Label label="Description">
                                <Textarea
                                    name="description"
                                    value={userRole?.description ?? ''}
                                    placeholder="Description"
                                    onChange={handleInputOnChange}
                                    rows={5}
                                    disabled={isAdmin}></Textarea>
                            </Label>
                        </div>
                    </div>

                    <div className="grid md:grid-cols-3">
                        <H3 className="text-lg">Permissions</H3>
                        <div className="flex flex-col gap-2 md:col-span-2">
                            {Object.keys(permissions).map((category) => (
                                <div
                                    className="flex flex-col border rounded-lg p-3 border-secondary"
                                    key={category}>
                                    <div className="items-top flex space-x-2 mb-4">
                                        <Checkbox
                                            id={`module-${category}`}
                                            checked={
                                                isModulePartiallySelected(
                                                    category,
                                                )
                                                    ? 'indeterminate'
                                                    : isModuleFullySelected(
                                                          category,
                                                      )
                                            }
                                            onCheckedChange={(checked) =>
                                                handleModuleCheckboxChange(
                                                    checked === true,
                                                    category,
                                                )
                                            }
                                        />
                                        <div className="grid gap-1.5 leading-none">
                                            <label
                                                htmlFor={`module-${category}`}
                                                className="text-sm font-bold leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
                                                {category}
                                            </label>
                                        </div>
                                    </div>
                                    <div className="flex flex-col gap-2.5 ml-6">
                                        {permissions[category].map(
                                            (permission: any) => (
                                                <div
                                                    className="items-top flex space-x-2"
                                                    key={permission.code}>
                                                    <Checkbox
                                                        id={permission.code}
                                                        value={permission.code}
                                                        checked={userPermissions.includes(
                                                            permission.code,
                                                        )}
                                                        onCheckedChange={(
                                                            checked,
                                                        ) =>
                                                            handlePermissionCheckboxChange(
                                                                checked ===
                                                                    true,
                                                                permission.code,
                                                            )
                                                        }
                                                    />
                                                    <div className="grid gap-1.5 leading-none">
                                                        <label
                                                            htmlFor={
                                                                permission.code
                                                            }
                                                            className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
                                                            {permission.name}
                                                        </label>
                                                        {permission.help && (
                                                            <p className="text-sm text-muted-foreground">
                                                                {
                                                                    permission.help
                                                                }
                                                            </p>
                                                        )}
                                                    </div>
                                                </div>
                                            ),
                                        )}
                                    </div>
                                </div>
                            ))}
                        </div>
                    </div>
                </CardContent>
            </Card>

            <FooterWrapper>
                <Button
                    size="sm"
                    variant={'back'}
                    onClick={() => router.back()}
                    disabled={
                        createSeaLogsGroupLoading ||
                        readOneSealLogsGroupLoading ||
                        updateSeaLogsGroupLoading
                    }>
                    Cancel
                </Button>
                {!isAdmin && (
                    <Button
                        size="sm"
                        onClick={handleSave}
                        disabled={
                            createSeaLogsGroupLoading ||
                            readOneSealLogsGroupLoading ||
                            updateSeaLogsGroupLoading
                        }>{`${roleID > 0 ? 'Update' : 'Create'} Role`}</Button>
                )}
            </FooterWrapper>
        </>
    )
}

export default UserRoleForm
